package com.lc.billion.icefire.game.biz.model.email;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.mailSdk.IMailSdk;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.support.LogReasons.ItemLogReason;
import com.lc.billion.icefire.protocol.structure.PsSimpleItem;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 * @sine 2015年10月21日 下午3:38:15
 * 
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "_class")
public abstract class AbstractEmail extends AbstractEntity implements IRolesEntity, IMailSdk {
	private static final long serialVersionUID = 156163077149130685L;

	@Setter
    @Getter
    @MongoId
	private Long id;
	/** 角色id */
	@MongoIndex
	private Long roleId;

	@Setter
    @Getter
    private long validTime;

	@Setter
    @Getter
    private int serverId;// 邮件发生服
	/**
	 * 携带物品
	 */
	@Getter
    private List<SimpleItem> attachments = new ArrayList<>();

	/**
	 * key: currency type val: value
	 * 获取 或者 损失资源
	 */
	@Setter
    @Getter
    protected Map<Currency, Integer> resources = new EnumMap<>(Currency.class);

	/**
	 * 是否已经阅读
	 */
	@Setter
    @Getter
    private int status;

	/**
	 * 奖励是否领取 旧版本不能支持不可领取、只能显示的奖励，特加此字段，将已读、未读与奖励是否领取区分开 0: 未领取奖励 1：已领取奖励
	 */
	@Setter
    @Getter
    private int isReward;

	/**
	 * 收藏
	 */
	@Setter
    @Getter
    private boolean lock;

	/**
	 * 邮件附件类型
	 */
	@Setter
    @Getter
    private ItemLogReason itemLogReason;

	/**
	 * 邮件产生的cityId，用于食物附件领取时所加的城市
	 */
	@Deprecated
    private String cityId;

	@Setter
    @Getter
    private String title;

	// 托管方
	@Getter
	@Setter
	private Long launchId;
	// 接受方
	@Getter
	@Setter
	private Long acceptId;

	public AbstractEmail() {

	}

	
	public boolean hasAttachment() {
		return !attachments.isEmpty();
	}

	public boolean hasResource() {
		return !resources.isEmpty();
	}

    public boolean canReward() {
		return false;
	}

    /**
	 * 邮件是否到期
     */
	public boolean isValid() {
		return this.validTime > TimeUtil.getNow();
	}

    public EmailType getType() {
		return getSubType().getEmailType();
	}

    public abstract EmailSubType getSubType();

	public abstract Object toInfo();

	public List<PsSimpleItem> wrapperReward() {
		List<PsSimpleItem> list = new ArrayList<>();
		for (SimpleItem item : getAttachments()) {
			PsSimpleItem psItem = new PsSimpleItem();
			psItem.setMetaId(item.getMetaId());
			psItem.setCount(item.getCount());
			list.add(psItem);
		}
		for (Map.Entry<Currency, Integer> entry : resources.entrySet()) {
			PsSimpleItem item = new PsSimpleItem();
			item.setMetaId(entry.getKey().getIdStr());
			item.setCount(entry.getValue());
			list.add(item);
		}
		return list;
	}


	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return this.id;
	}

	@Override
	public Long getGroupingId() {
		return this.id;
	}

	@Override
	public Long getRoleId() {
		return roleId;
	}

	@Override
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

    @Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}
}
